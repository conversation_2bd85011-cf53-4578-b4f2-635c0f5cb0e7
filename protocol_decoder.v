//==============================================================================
// 基于1-Wire总线的协议解码器顶层模块
// 集成1-Wire接口和数据解析功能
//==============================================================================

module protocol_decoder (
    input         clk,           // 系统时钟 100MHz
    input         rst_n,         // 复位信号，低有效
    inout         onewire_io,    // 1-Wire总线信号
    
    // 解析结果输出
    output [31:0] packet_no,     // 解析出的包号
    output [191:0] user_data,    // 解析出的用户数据(24字节)
    output        crc_valid,     // CRC校验结果
    output        parse_done,    // 解析完成信号
    output        parse_error,   // 解析错误信号
    
    // 1-Wire状态输出
    output        reset_detected,   // 复位脉冲检测
    output        presence_detected, // 存在脉冲检测
    output        onewire_error     // 1-Wire错误标志
);

    // 1-Wire接口与数据解析器之间的连接信号
    wire [7:0]  byte_data;
    wire        byte_valid;

    // 实例化1-Wire接口模块
    onewire_interface u_onewire_interface (
        .clk(clk),
        .rst_n(rst_n),
        .onewire_io(onewire_io),
        .byte_data(byte_data),
        .byte_valid(byte_valid),
        .reset_detected(reset_detected),
        .presence_detected(presence_detected),
        .error_flag(onewire_error)
    );

    // 实例化数据解析模块
    data_parser u_data_parser (
        .clk(clk),
        .rst_n(rst_n),
        .data_in(byte_data),
        .data_valid(byte_valid),
        .packet_no(packet_no),
        .user_data(user_data),
        .crc_valid(crc_valid),
        .parse_done(parse_done),
        .parse_error(parse_error)
    );

endmodule
