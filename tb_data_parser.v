//==============================================================================
// 协议解码器测试文件 (包含1-Wire接口和数据解析)
//==============================================================================

`timescale 1ns/1ps

module tb_protocol_decoder;

    // 测试信号
    reg         clk;
    reg         rst_n;
    wire        onewire_io;
    reg         onewire_drive;  // 用于驱动1-Wire总线的信号

    wire [31:0] packet_no;
    wire [191:0] user_data;
    wire        crc_valid;
    wire        parse_done;
    wire        parse_error;
    wire        reset_detected;
    wire        presence_detected;
    wire        onewire_error;

    // 1-Wire总线驱动逻辑 (开漏模拟)
    assign onewire_io = onewire_drive ? 1'b0 : 1'bz;

    // 实例化被测模块
    protocol_decoder u_protocol_decoder (
        .clk(clk),
        .rst_n(rst_n),
        .onewire_io(onewire_io),
        .packet_no(packet_no),
        .user_data(user_data),
        .crc_valid(crc_valid),
        .parse_done(parse_done),
        .parse_error(parse_error),
        .reset_detected(reset_detected),
        .presence_detected(presence_detected),
        .onewire_error(onewire_error)
    );

    // 时钟生成
    initial begin
        clk = 0;
        forever #5 clk = ~clk;  // 100MHz时钟
    end

    // 1-Wire时序参数 (基于100MHz时钟)
    localparam RESET_PULSE_TIME = 50000;  // 500us复位脉冲
    localparam PRESENCE_PULSE_TIME = 15000; // 150us存在脉冲
    localparam SLOT_TIME = 6000;          // 60us时隙时间
    localparam WRITE_0_TIME = 6000;       // 60us写0时间
    localparam WRITE_1_TIME = 600;        // 6us写1时间
    localparam RECOVERY_TIME = 100;       // 1us恢复时间

    // 测试数据帧(32字节)
    reg [7:0] test_frame [0:31];
    integer i, j;

    // 1-Wire写位任务
    task write_bit;
        input bit_value;
        begin
            if (bit_value) begin
                // 写1: 短脉冲
                onewire_drive = 1'b1;
                #(WRITE_1_TIME * 10);  // 6us
                onewire_drive = 1'b0;
                #((SLOT_TIME - WRITE_1_TIME) * 10);  // 剩余时间
            end else begin
                // 写0: 长脉冲
                onewire_drive = 1'b1;
                #(WRITE_0_TIME * 10);  // 60us
                onewire_drive = 1'b0;
                #(RECOVERY_TIME * 10);  // 1us恢复时间
            end
        end
    endtask

    // 1-Wire写字节任务
    task write_byte;
        input [7:0] byte_data;
        begin
            for (j = 0; j < 8; j = j + 1) begin
                write_bit(byte_data[j]);  // LSB first
            end
        end
    endtask

    // 1-Wire复位序列任务
    task reset_sequence;
        begin
            // 发送复位脉冲
            onewire_drive = 1'b1;
            #(RESET_PULSE_TIME * 10);  // 500us
            onewire_drive = 1'b0;

            // 等待存在脉冲窗口
            #(PRESENCE_PULSE_TIME * 10);  // 150us
        end
    endtask

    // 测试流程
    initial begin
        // 初始化
        rst_n = 0;
        onewire_drive = 0;  // 释放总线

        // 构造测试数据帧
        test_frame[0]  = 8'hAA;  // 帧头
        test_frame[1]  = 8'h55;  // 帧头
        test_frame[2]  = 8'h20;  // 长度标识(32字节)
        test_frame[3]  = 8'h00;  // 包号高字节
        test_frame[4]  = 8'h00;
        test_frame[5]  = 8'h04;
        test_frame[6]  = 8'hD2;  // 包号=1234

        // 用户数据区(24字节，填充0x00)
        for (i = 7; i <= 30; i = i + 1) begin
            test_frame[i] = 8'h00;
        end

        // CRC8字段(需要根据实际计算填入)
        test_frame[31] = 8'h6E;  // 预计算的CRC值

        // 复位
        #200 rst_n = 1;
        #1000;

        // 发送1-Wire复位序列
        $display("发送1-Wire复位序列...");
        reset_sequence();

        // 发送测试数据帧
        $display("开始发送测试数据帧...");
        for (i = 0; i < 32; i = i + 1) begin
            $display("发送字节[%0d]: 0x%02h", i, test_frame[i]);
            write_byte(test_frame[i]);
        end

        // 等待解析完成
        #100000;  // 等待足够长时间

        if (parse_done) begin
            $display("数据解析完成!");
            $display("包号: %0d (0x%08h)", packet_no, packet_no);
            $display("CRC校验: %s", crc_valid ? "通过" : "失败");
        end else if (parse_error) begin
            $display("数据解析错误!");
        end else begin
            $display("解析超时!");
        end

        #1000;
        $finish;
    end

    // 监控信号变化
    initial begin
        $monitor("时间=%0t, 复位检测=%b, 存在检测=%b, 解析完成=%b, 解析错误=%b, 1-Wire错误=%b",
                 $time, reset_detected, presence_detected, parse_done, parse_error, onewire_error);
    end

endmodule
