//==============================================================================
// 数据解析模块测试文件
//==============================================================================

`timescale 1ns/1ps

module tb_data_parser;

    // 测试信号
    reg         clk;
    reg         rst_n;
    reg  [7:0]  data_in;
    reg         data_valid;
    
    wire [31:0] packet_no;
    wire [191:0] user_data;
    wire        crc_valid;
    wire        parse_done;
    wire        parse_error;

    // 实例化被测模块
    data_parser u_data_parser (
        .clk(clk),
        .rst_n(rst_n),
        .data_in(data_in),
        .data_valid(data_valid),
        .packet_no(packet_no),
        .user_data(user_data),
        .crc_valid(crc_valid),
        .parse_done(parse_done),
        .parse_error(parse_error)
    );

    // 时钟生成
    initial begin
        clk = 0;
        forever #5 clk = ~clk;  // 100MHz时钟
    end

    // 测试数据帧(32字节)
    reg [7:0] test_frame [0:31];
    integer i;

    // 测试流程
    initial begin
        // 初始化
        rst_n = 0;
        data_in = 8'h00;
        data_valid = 0;
        
        // 构造测试数据帧
        test_frame[0]  = 8'hAA;  // 帧头
        test_frame[1]  = 8'h55;  // 帧头
        test_frame[2]  = 8'h20;  // 长度标识(32字节)
        test_frame[3]  = 8'h00;  // 包号高字节
        test_frame[4]  = 8'h00;
        test_frame[5]  = 8'h04;
        test_frame[6]  = 8'hD2;  // 包号=1234
        
        // 用户数据区(24字节，填充0x00)
        for (i = 7; i <= 30; i = i + 1) begin
            test_frame[i] = 8'h00;
        end
        
        // CRC8字段(需要根据实际计算填入)
        test_frame[31] = 8'h00;  // 暂时填0，实际应该计算CRC
        
        // 复位
        #20 rst_n = 1;
        #20;
        
        // 发送测试数据帧
        $display("开始发送测试数据帧...");
        for (i = 0; i < 32; i = i + 1) begin
            @(posedge clk);
            data_in = test_frame[i];
            data_valid = 1;
            $display("发送字节[%0d]: 0x%02h", i, test_frame[i]);
            @(posedge clk);
            data_valid = 0;
            #10;  // 等待几个时钟周期
        end
        
        // 等待解析完成
        wait(parse_done || parse_error);
        
        if (parse_done) begin
            $display("数据解析完成!");
            $display("包号: %0d (0x%08h)", packet_no, packet_no);
            $display("CRC校验: %s", crc_valid ? "通过" : "失败");
        end else if (parse_error) begin
            $display("数据解析错误!");
        end
        
        #100;
        $finish;
    end

    // 监控信号变化
    initial begin
        $monitor("时间=%0t, 状态=%0d, 字节计数=%0d, 解析完成=%b, 解析错误=%b", 
                 $time, u_data_parser.state, u_data_parser.byte_cnt, parse_done, parse_error);
    end

endmodule
