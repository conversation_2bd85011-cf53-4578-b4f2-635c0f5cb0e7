//==============================================================================
// CRC8 计算模块
// 多项式: 0x31 (x^8 + x^5 + x^4 + 1)
// 初始值: 0x00
// 高位在前，无反序，无最终异或值
// 组合逻辑实现，一个时钟周期内完成8bit CRC计算
//==============================================================================

module crc8_calc (
    input  [7:0] data_in,    // 输入8位数据
    input  [7:0] crc_in,     // 当前CRC值
    output [7:0] crc_out     // 输出新的CRC值
);

    // CRC8多项式0x31对应的生成多项式: x^8 + x^5 + x^4 + 1
    // 组合逻辑实现8位并行CRC计算
    
    wire [7:0] data_xor_crc;
    wire [7:0] crc_temp;
    
    // 将输入数据与当前CRC进行异或
    assign data_xor_crc = data_in ^ crc_in;
    
    // 8位并行CRC计算逻辑
    // 根据CRC8多项式0x31展开的组合逻辑
    assign crc_temp[0] = data_xor_crc[0] ^ data_xor_crc[4] ^ data_xor_crc[5];
    assign crc_temp[1] = data_xor_crc[1] ^ data_xor_crc[5] ^ data_xor_crc[6];
    assign crc_temp[2] = data_xor_crc[2] ^ data_xor_crc[6] ^ data_xor_crc[7];
    assign crc_temp[3] = data_xor_crc[3] ^ data_xor_crc[7];
    assign crc_temp[4] = data_xor_crc[0] ^ data_xor_crc[4];
    assign crc_temp[5] = data_xor_crc[0] ^ data_xor_crc[1] ^ data_xor_crc[4] ^ data_xor_crc[5];
    assign crc_temp[6] = data_xor_crc[1] ^ data_xor_crc[2] ^ data_xor_crc[5] ^ data_xor_crc[6];
    assign crc_temp[7] = data_xor_crc[2] ^ data_xor_crc[3] ^ data_xor_crc[6] ^ data_xor_crc[7];
    
    assign crc_out = crc_temp;

endmodule
