# Makefile for Verilog simulation

# 仿真器设置
SIM = iverilog
VVP = vvp
GTKWAVE = gtkwave

# 源文件
CRC8_SRC = crc8_calc.v
PARSER_SRC = data_parser.v crc8_calc.v
CRC8_TB = tb_crc8_calc.v
PARSER_TB = tb_data_parser.v

# 输出文件
CRC8_OUT = crc8_test
PARSER_OUT = parser_test

# 默认目标
all: test_crc8 test_parser

# 编译和运行CRC8测试
test_crc8: $(CRC8_OUT)
	$(VVP) $(CRC8_OUT)

$(CRC8_OUT): $(CRC8_SRC) $(CRC8_TB)
	$(SIM) -o $(CRC8_OUT) $(CRC8_SRC) $(CRC8_TB)

# 编译和运行数据解析器测试
test_parser: $(PARSER_OUT)
	$(VVP) $(PARSER_OUT)

$(PARSER_OUT): $(PARSER_SRC) $(PARSER_TB)
	$(SIM) -o $(PARSER_OUT) $(PARSER_SRC) $(PARSER_TB)

# 生成波形文件
wave_crc8: $(CRC8_OUT)
	$(VVP) $(CRC8_OUT) -vcd
	$(GTKWAVE) crc8_test.vcd &

wave_parser: $(PARSER_OUT)
	$(VVP) $(PARSER_OUT) -vcd
	$(GTKWAVE) parser_test.vcd &

# 清理
clean:
	rm -f $(CRC8_OUT) $(PARSER_OUT) *.vcd

# 语法检查
syntax_check:
	$(SIM) -t null $(CRC8_SRC)
	$(SIM) -t null $(PARSER_SRC)

.PHONY: all test_crc8 test_parser wave_crc8 wave_parser clean syntax_check
