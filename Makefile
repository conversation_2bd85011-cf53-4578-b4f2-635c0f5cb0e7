# Makefile for Verilog simulation

# 仿真器设置
SIM = iverilog
VVP = vvp
GTKWAVE = gtkwave

# 源文件
CRC8_SRC = crc8_calc.v
PARSER_SRC = data_parser.v crc8_calc.v
ONEWIRE_SRC = onewire_interface.v
PROTOCOL_SRC = protocol_decoder.v data_parser.v crc8_calc.v onewire_interface.v

# 测试文件
CRC8_TB = tb_crc8_calc.v
PARSER_TB = tb_data_parser_simple.v
PROTOCOL_TB = tb_data_parser.v

# 输出文件
CRC8_OUT = crc8_test
PARSER_OUT = parser_test
PROTOCOL_OUT = protocol_test

# 默认目标
all: test_crc8 test_parser test_protocol

# 编译和运行CRC8测试
test_crc8: $(CRC8_OUT)
	$(VVP) $(CRC8_OUT)

$(CRC8_OUT): $(CRC8_SRC) $(CRC8_TB)
	$(SIM) -o $(CRC8_OUT) $(CRC8_SRC) $(CRC8_TB)

# 编译和运行数据解析器测试(简单版本)
test_parser: $(PARSER_OUT)
	$(VVP) $(PARSER_OUT)

$(PARSER_OUT): $(PARSER_SRC) $(PARSER_TB)
	$(SIM) -o $(PARSER_OUT) $(PARSER_SRC) $(PARSER_TB)

# 编译和运行完整协议测试(包含1-Wire接口)
test_protocol: $(PROTOCOL_OUT)
	$(VVP) $(PROTOCOL_OUT)

$(PROTOCOL_OUT): $(PROTOCOL_SRC) $(PROTOCOL_TB)
	$(SIM) -o $(PROTOCOL_OUT) $(PROTOCOL_SRC) $(PROTOCOL_TB)

# 生成波形文件
wave_crc8: $(CRC8_OUT)
	$(VVP) $(CRC8_OUT) -vcd
	$(GTKWAVE) crc8_test.vcd &

wave_parser: $(PARSER_OUT)
	$(VVP) $(PARSER_OUT) -vcd
	$(GTKWAVE) parser_test.vcd &

wave_protocol: $(PROTOCOL_OUT)
	$(VVP) $(PROTOCOL_OUT) -vcd
	$(GTKWAVE) protocol_test.vcd &

# 清理
clean:
	rm -f $(CRC8_OUT) $(PARSER_OUT) $(PROTOCOL_OUT) *.vcd

# 语法检查
syntax_check:
	$(SIM) -t null $(CRC8_SRC)
	$(SIM) -t null $(PARSER_SRC)
	$(SIM) -t null $(ONEWIRE_SRC)
	$(SIM) -t null $(PROTOCOL_SRC)

.PHONY: all test_crc8 test_parser test_protocol wave_crc8 wave_parser wave_protocol clean syntax_check
