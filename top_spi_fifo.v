// File: top_spi_fifo.v
module top_spi_fifo (
    input   wire     clk,
    input   wire     rst_n,               
    // SPI接口信号
    input  wire      spi_mosi,     // SPI数据输入
    input  wire      spi_sclk,     // SPI时钟
    input  wire      spi_cs_n,     // SPI片选
    output wire      spi_miso,     // SPI数据输出

    output wire [7:0]  spi_tx_data        // SPI发送数据(用于调试)
);

parameter DATA_WIDTH = 8;        // 数据位宽 (默认8位)
parameter SPI_MODE = 0;          // SPI模式 (0-3)

// 信号声明
wire [7:0] spi_rec_data;        // 连接SPI接收数据到FIFO写数据
wire [7:0] rec_byte;
wire [7:0] rd_data;
// wire [7:0] tx_data;
wire fifo_wr_en;                 // 使用SPI的数据有效信号
wire parsing;                    // 解析状态信号
wire transmit_done;
wire rd_empty;
wire rd_en;
// 实例化spi_slave模块
//spi_slave #(
//    .DATA_WIDTH(DATA_WIDTH),
//    .SPI_MODE(SPI_MODE)
//) u_spi_slave (
//    .clk(clk),
//    .rst_n(rst_n),           
//
//    .sclk(spi_sclk),         // 修复信号名错误
//    .cs_n(spi_cs_n),         // 连接SPI CS信号
//    .mosi(spi_mosi),         // 连接SPI MOSI信号
//    .miso(spi_miso),         // 连接SPI MISO信号
//
//    .tx_data(8'd0),
//    .rx_data_out(spi_rec_data),  // 将SPI接收数据连接到FIFO写数据
//    // .rx_valid(),     // 暴露接收有效信号
//    .fifo_wr_en(fifo_wr_en), // 使用SPI的数据有效信号
//    .transmit_done(transmit_done),
//    .rec_byte(rec_byte)      // 暴露接收字节计数器
//);
// wire [DATA_WIDTH-1:0] unused_debug_rx_shift;
// wire [7:0] unused_debug_rx_data_reg;
// SPI从设备模块
spi_slave #(
    .DATA_WIDTH(DATA_WIDTH),
    .SPI_MODE(SPI_MODE)
) u_spi_slave (
    .clk(clk),                              // input
    .rst_n(rst_n),                          // input
    .sclk(spi_sclk),                            // input
    .cs_n(spi_cs_n),                            // input
    .mosi(spi_mosi),                            // input
    .miso(spi_miso),                            // output
    .rx_data_out(spi_tx_data),              // output
    .tx_data(),                      // input
    .fifo_wr_en(fifo_wr_en),                // output
    .rec_byte(rec_byte),                    // output
    .transmit_done(transmit_done),          // output
    .tx_ready(),                    // output
    .error(),                     // output
    .debug_rx_shift(),                      // output (调试用)
    .debug_rx_data_reg()                    // output (调试用)
);

// spi_fifo u_spi_fifo (
//   .clk(clk),                      // input
//   .rst(~rst_n),                      // input
//   .wr_en(fifo_wr_en),                  // input
//   .wr_data(spi_rec_data),              // input [7:0]
//   .wr_full(),              // output
//   .almost_full(),      // output
//   .rd_en(),                  // input
//   .rd_data(),              // output [7:0]
//   .rd_empty(),            // output
//   .almost_empty()     // output
// );
//spi_fifo u_256x8b_fifo (
//    .clk(clk),                      // input
//    .rst(~rst_n),                    // 
//    .wr_en(fifo_wr_en),            // 使用SPI的数据有效信号
//    .wr_data(spi_rec_data),        // 使用SPI的接收数据
//    .wr_full(),             // output
//    .almost_full(),     // output
//    .rd_en(rd_en),                 // input
//    .rd_data(rd_data),             // output [7:0]
//    .rd_empty(rd_empty),           // output
//    .almost_empty()    // output
//);

// 实例化parse_spi模块
// parse_spi u_parse_spi (
//     .clk(clk),
//     .rst_n(rst_n),
//     .transmit_done(transmit_done), 
//     .rec_byte(rec_byte),
//     .rd_data(rd_data),
//     .rd_empty(rd_empty),
//     .rd_en(rd_en),
//     .data_out(spi_tx_data),            // 解析后的数据输出
//     .parsing(parsing)
// );

endmodule