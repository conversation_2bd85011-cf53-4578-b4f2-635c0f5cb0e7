# 基于1-Wire总线的协议解码器

本项目实现了基于1-Wire总线协议的32字节数据帧解码器，包含CRC8校验功能。

## 协议规范

### 数据帧格式 (32字节)
| 字段 | 字节偏移 | 长度 | 说明 |
|------|----------|------|------|
| 帧头 | 0-1 | 2字节 | 固定为0xAA 0x55 |
| 长度标识 | 2 | 1字节 | 固定为0x20 (32字节) |
| 包号 | 3-6 | 4字节 | 大端序无符号整数 |
| 用户数据 | 7-30 | 24字节 | 用户自定义数据 |
| CRC8校验 | 31 | 1字节 | 基于包号+用户数据计算 |

### CRC8参数
- 多项式: 0x31 (x^8 + x^5 + x^4 + 1)
- 初始值: 0x00
- 高位在前，无反序，无最终异或值
- 校验范围: 包号(4字节) + 用户数据(24字节) = 28字节

## 模块架构

### 1. CRC8计算模块 (`crc8_calc.v`)
- **功能**: 组合逻辑实现8位CRC8计算
- **输入**: 8位数据，当前CRC值
- **输出**: 新的CRC值
- **特点**: 一个时钟周期内完成计算

### 2. 1-Wire接口模块 (`onewire_interface.v`)
- **功能**: 实现1-Wire总线物理层协议
- **特性**:
  - 复位脉冲检测 (480-960μs)
  - 存在脉冲检测 (60-240μs)
  - 数据位解析 (区分0和1)
  - 字节组装 (LSB first)
- **时序**: 基于100MHz系统时钟

### 3. 数据解析模块 (`data_parser.v`)
- **功能**: 解析32字节数据帧
- **状态机**: 
  - IDLE → HEADER_CHECK → LENGTH_CHECK → DATA_RECEIVE → CRC_CALC → CRC_CHECK → DONE
- **CRC计算**: 独立状态机，逐字节计算28字节CRC
- **输出**: 包号、用户数据、CRC校验结果

### 4. 协议解码器顶层模块 (`protocol_decoder.v`)
- **功能**: 集成1-Wire接口和数据解析功能
- **接口**: 1-Wire总线输入，解析结果输出

## 文件说明

### 核心模块
- `crc8_calc.v` - CRC8计算模块
- `onewire_interface.v` - 1-Wire总线接口
- `data_parser.v` - 数据解析模块
- `protocol_decoder.v` - 顶层协议解码器

### 测试文件
- `tb_crc8_calc.v` - CRC8模块测试
- `tb_data_parser_simple.v` - 数据解析模块简单测试
- `tb_data_parser.v` - 完整协议测试(包含1-Wire时序)

### 构建文件
- `Makefile` - 编译和仿真脚本

## 使用方法

### 编译和测试

```bash
# 测试CRC8模块
make test_crc8

# 测试数据解析模块(简单版本)
make test_parser

# 测试完整协议(包含1-Wire接口)
make test_protocol

# 语法检查
make syntax_check

# 清理文件
make clean
```

### 生成波形文件

```bash
# 生成CRC8测试波形
make wave_crc8

# 生成数据解析测试波形
make wave_parser

# 生成完整协议测试波形
make wave_protocol
```

## 设计特点

### 1. 实际的CRC计算
- 使用状态机逐字节计算28字节的CRC
- 不使用简化的函数实现
- 28个时钟周期完成CRC计算

### 2. 基于1-Wire总线协议
- 实现真实的1-Wire物理层时序
- 支持复位和存在脉冲检测
- 正确的位时序解析

### 3. 标准Verilog语法
- 使用标准的always块和assign语句
- 三段式状态机设计
- 组合逻辑和时序逻辑分离

### 4. 模块化设计
- 各模块功能独立，接口清晰
- 便于集成和测试
- 支持独立验证

## 时序参数

基于100MHz系统时钟的1-Wire时序参数：

| 参数 | 时间 | 时钟周期数 |
|------|------|------------|
| 复位脉冲 | 480-960μs | 48000-96000 |
| 存在脉冲 | 60-240μs | 6000-24000 |
| 写0时隙 | 60μs | 6000 |
| 写1时隙 | 6μs | 600 |
| 采样时间 | 15μs | 1500 |

## 注意事项

1. **CRC计算**: 必须等待CRC计算完成后才能进行校验
2. **1-Wire时序**: 严格按照1-Wire协议时序要求实现
3. **大端序**: 包号采用大端序编码
4. **LSB优先**: 1-Wire数据传输采用LSB优先
5. **开漏输出**: 1-Wire总线为开漏结构，需要上拉电阻
