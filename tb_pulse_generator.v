`timescale 1ns / 1ps

module tb_pulse_generator;

// 参数定义
parameter CLK_FREQ = 125_000_000;    // 125MHz时钟
parameter CLK_PERIOD = 8;            // 8ns时钟周期
parameter MIN_PULSE_WIDTH_US = 40;   // 最小脉冲宽度40us

// 测试信号定义
reg                 clk;
reg                 rst_n;
reg  [31:0]         fpga_trigger_time;
reg                 fpga_trigger_method;
reg                 device_start_stop;
reg  [31:0]         pulse_width_us;
wire                pulse_out;

// 实例化被测模块
pulse_generator #(
    .CLK_FREQ(CLK_FREQ),
    .MIN_PULSE_WIDTH_US(MIN_PULSE_WIDTH_US)
) uut (
    .clk(clk),
    .rst_n(rst_n),
    .fpga_trigger_time(fpga_trigger_time),
    .fpga_trigger_method(fpga_trigger_method),
    .device_start_stop(device_start_stop),
    .pulse_width_us(pulse_width_us),
    .pulse_out(pulse_out)
);

// 时钟生成
initial begin
    clk = 0;
    forever #(CLK_PERIOD/2) clk = ~clk;
end

// 测试向量
initial begin
    // 初始化信号
    rst_n = 0;
    fpga_trigger_time = 32'd0;
    fpga_trigger_method = 1'b0;
    device_start_stop = 1'b0;
    pulse_width_us = 32'd50;  // 50us脉冲宽度
    
    // 等待复位完成
    #100;
    rst_n = 1;
    #100;
    
    $display("=== 脉冲发生器测试开始 ===");
    
    // 测试1: 基本功能测试 - fpga_trigger_method=1, 周期2ms, 脉冲宽度50us
    $display("测试1: 基本功能测试");
    fpga_trigger_time = 32'd2000;      // 2ms = 2000us
    fpga_trigger_method = 1'b1;        // 启用触发
    pulse_width_us = 32'd50;           // 50us脉冲宽度
    
    #1000;  // 等待1us
    device_start_stop = 1'b1;          // 启动信号上升沿
    $display("时间: %0t ns - 设备启动信号上升沿", $time);
    
    // 等待几个脉冲周期
    #20_000_000;  // 等待20ms，应该看到约10个脉冲
    
    device_start_stop = 1'b0;          // 停止信号下降沿
    $display("时间: %0t ns - 设备停止信号下降沿", $time);
    
    #5_000_000;   // 等待5ms，确认脉冲停止
    
    // 测试2: fpga_trigger_method=0时不应该产生脉冲
    $display("测试2: fpga_trigger_method=0测试");
    fpga_trigger_method = 1'b0;        // 禁用触发
    
    #1000;
    device_start_stop = 1'b1;          // 启动信号上升沿
    $display("时间: %0t ns - fpga_trigger_method=0时启动", $time);
    
    #10_000_000;  // 等待10ms，不应该有脉冲
    
    device_start_stop = 1'b0;          // 停止信号
    #1_000_000;
    
    // 测试3: 最小脉冲宽度测试
    $display("测试3: 最小脉冲宽度测试");
    fpga_trigger_method = 1'b1;
    fpga_trigger_time = 32'd5000;      // 5ms周期
    pulse_width_us = 32'd20;           // 尝试设置20us（小于最小值40us）
    
    #1000;
    device_start_stop = 1'b1;
    $display("时间: %0t ns - 最小脉冲宽度测试开始", $time);
    
    #15_000_000;  // 等待15ms
    
    device_start_stop = 1'b0;
    #2_000_000;
    
    // 测试4: 快速周期测试
    $display("测试4: 快速周期测试");
    fpga_trigger_time = 32'd1000;      // 1ms周期
    pulse_width_us = 32'd100;          // 100us脉冲宽度
    
    #1000;
    device_start_stop = 1'b1;
    $display("时间: %0t ns - 快速周期测试开始", $time);
    
    #10_000_000;  // 等待10ms
    
    device_start_stop = 1'b0;
    #2_000_000;
    
    $display("=== 脉冲发生器测试完成 ===");
    $finish;
end

// 脉冲监控
reg [31:0] pulse_count;
reg        pulse_out_prev;

initial begin
    pulse_count = 0;
    pulse_out_prev = 0;
end

always @(posedge clk) begin
    pulse_out_prev <= pulse_out;
    
    // 检测脉冲上升沿
    if (pulse_out && !pulse_out_prev) begin
        pulse_count <= pulse_count + 1;
        $display("时间: %0t ns - 脉冲 #%0d 开始", $time, pulse_count + 1);
    end
    
    // 检测脉冲下降沿
    if (!pulse_out && pulse_out_prev) begin
        $display("时间: %0t ns - 脉冲 #%0d 结束", $time, pulse_count);
    end
end

// 脉冲宽度测量
reg [31:0] pulse_start_time;
reg [31:0] pulse_width_measured;

always @(posedge clk) begin
    if (pulse_out && !pulse_out_prev) begin
        pulse_start_time <= $time;
    end
    
    if (!pulse_out && pulse_out_prev) begin
        pulse_width_measured <= $time - pulse_start_time;
        $display("测量到的脉冲宽度: %0d ns (%0.1f us)", 
                pulse_width_measured, pulse_width_measured/1000.0);
    end
end

// 波形文件生成
initial begin
    $dumpfile("tb_pulse_generator.vcd");
    $dumpvars(0, tb_pulse_generator);
end

endmodule
