//==============================================================================
// CRC8计算模块测试文件
//==============================================================================

`timescale 1ns/1ps

module tb_crc8_calc;

    // 测试信号
    reg  [7:0] data_in;
    reg  [7:0] crc_in;
    wire [7:0] crc_out;

    // 实例化被测模块
    crc8_calc u_crc8_calc (
        .data_in(data_in),
        .crc_in(crc_in),
        .crc_out(crc_out)
    );

    // 测试流程
    initial begin
        $display("开始CRC8计算模块测试...");
        
        // 测试用例1: 初始CRC=0, 数据=0x00
        crc_in = 8'h00;
        data_in = 8'h00;
        #10;
        $display("测试1: CRC_IN=0x%02h, DATA_IN=0x%02h, CRC_OUT=0x%02h", crc_in, data_in, crc_out);
        
        // 测试用例2: 初始CRC=0, 数据=0xFF
        crc_in = 8'h00;
        data_in = 8'hFF;
        #10;
        $display("测试2: CRC_IN=0x%02h, DATA_IN=0x%02h, CRC_OUT=0x%02h", crc_in, data_in, crc_out);
        
        // 测试用例3: 初始CRC=0, 数据=0xAA
        crc_in = 8'h00;
        data_in = 8'hAA;
        #10;
        $display("测试3: CRC_IN=0x%02h, DATA_IN=0x%02h, CRC_OUT=0x%02h", crc_in, data_in, crc_out);
        
        // 测试用例4: 连续计算多个字节的CRC
        $display("\n连续计算CRC测试:");
        crc_in = 8'h00;
        
        // 计算包号1234 (0x00, 0x00, 0x04, 0xD2) 的CRC
        data_in = 8'h00;  // 包号第1字节
        #10;
        $display("字节1: DATA=0x%02h, CRC=0x%02h", data_in, crc_out);
        crc_in = crc_out;
        
        data_in = 8'h00;  // 包号第2字节
        #10;
        $display("字节2: DATA=0x%02h, CRC=0x%02h", data_in, crc_out);
        crc_in = crc_out;
        
        data_in = 8'h04;  // 包号第3字节
        #10;
        $display("字节3: DATA=0x%02h, CRC=0x%02h", data_in, crc_out);
        crc_in = crc_out;
        
        data_in = 8'hD2;  // 包号第4字节
        #10;
        $display("字节4: DATA=0x%02h, CRC=0x%02h", data_in, crc_out);
        
        $display("\nCRC8计算模块测试完成!");
        $finish;
    end

endmodule
