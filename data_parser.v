//==============================================================================
// 数据解析模块
// 解析32字节数据帧，包含帧头、长度、包号、用户数据和CRC8校验
// 帧格式: 帧头(2B) + 长度(1B) + 包号(4B) + 用户数据(24B) + CRC8(1B)
//==============================================================================

module data_parser (
    input         clk,           // 时钟信号
    input         rst_n,         // 复位信号，低有效
    input  [7:0]  data_in,       // 输入数据，逐字节输入
    input         data_valid,    // 数据有效信号
    
    output reg [31:0] packet_no,     // 解析出的包号
    output reg [191:0] user_data,    // 解析出的用户数据(24字节)
    output reg        crc_valid,     // CRC校验结果
    output reg        parse_done,    // 解析完成信号
    output reg        parse_error    // 解析错误信号
);

    // 状态定义
    localparam IDLE         = 3'b000;
    localparam HEADER_CHECK = 3'b001;
    localparam LENGTH_CHECK = 3'b010;
    localparam DATA_RECEIVE = 3'b011;
    localparam CRC_CHECK    = 3'b100;
    localparam DONE         = 3'b101;
    localparam ERROR        = 3'b110;

    // 内部信号
    reg [2:0]   state, next_state;
    reg [4:0]   byte_cnt;           // 字节计数器(0-31)
    reg [7:0]   frame_buffer [0:31]; // 32字节帧缓存
    reg [7:0]   crc_calc;           // 计算得到的CRC值
    reg [7:0]   crc_received;       // 接收到的CRC值
    
    // CRC计算相关信号
    reg [7:0]   crc_data_in;
    reg [7:0]   crc_current;
    wire [7:0]  crc_next;
    reg [4:0]   crc_byte_cnt;       // CRC计算字节计数器
    
    // 实例化CRC8计算模块
    crc8_calc u_crc8_calc (
        .data_in(crc_data_in),
        .crc_in(crc_current),
        .crc_out(crc_next)
    );

    // 状态机时序逻辑
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            state <= IDLE;
        end else begin
            state <= next_state;
        end
    end

    // 状态机组合逻辑
    always @(*) begin
        next_state = state;
        case (state)
            IDLE: begin
                if (data_valid && data_in == 8'hAA)
                    next_state = HEADER_CHECK;
            end
            
            HEADER_CHECK: begin
                if (data_valid) begin
                    if (data_in == 8'h55)
                        next_state = LENGTH_CHECK;
                    else
                        next_state = ERROR;
                end
            end
            
            LENGTH_CHECK: begin
                if (data_valid) begin
                    if (data_in == 8'h20)  // 长度为32字节
                        next_state = DATA_RECEIVE;
                    else
                        next_state = ERROR;
                end
            end
            
            DATA_RECEIVE: begin
                if (data_valid && byte_cnt == 5'd31)
                    next_state = CRC_CHECK;
            end
            
            CRC_CHECK: begin
                next_state = DONE;
            end
            
            DONE: begin
                next_state = IDLE;
            end
            
            ERROR: begin
                next_state = IDLE;
            end
            
            default: next_state = IDLE;
        endcase
    end

    // 数据接收和处理逻辑
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            byte_cnt <= 5'd0;
            packet_no <= 32'd0;
            user_data <= 192'd0;
            crc_valid <= 1'b0;
            parse_done <= 1'b0;
            parse_error <= 1'b0;
            crc_calc <= 8'd0;
            crc_received <= 8'd0;
            crc_current <= 8'd0;
            crc_byte_cnt <= 5'd0;
        end else begin
            case (state)
                IDLE: begin
                    byte_cnt <= 5'd0;
                    parse_done <= 1'b0;
                    parse_error <= 1'b0;
                    crc_valid <= 1'b0;
                    crc_current <= 8'd0;
                    crc_byte_cnt <= 5'd0;
                    if (data_valid && data_in == 8'hAA) begin
                        frame_buffer[0] <= data_in;
                        byte_cnt <= 5'd1;
                    end
                end
                
                HEADER_CHECK: begin
                    if (data_valid) begin
                        frame_buffer[byte_cnt] <= data_in;
                        byte_cnt <= byte_cnt + 1'b1;
                    end
                end
                
                LENGTH_CHECK: begin
                    if (data_valid) begin
                        frame_buffer[byte_cnt] <= data_in;
                        byte_cnt <= byte_cnt + 1'b1;
                    end
                end
                
                DATA_RECEIVE: begin
                    if (data_valid) begin
                        frame_buffer[byte_cnt] <= data_in;
                        byte_cnt <= byte_cnt + 1'b1;
                    end
                end
                
                CRC_CHECK: begin
                    // 提取包号(字节3-6，大端序)
                    packet_no <= {frame_buffer[3], frame_buffer[4], frame_buffer[5], frame_buffer[6]};
                    
                    // 提取用户数据(字节7-30)
                    user_data <= {frame_buffer[7],  frame_buffer[8],  frame_buffer[9],  frame_buffer[10],
                                  frame_buffer[11], frame_buffer[12], frame_buffer[13], frame_buffer[14],
                                  frame_buffer[15], frame_buffer[16], frame_buffer[17], frame_buffer[18],
                                  frame_buffer[19], frame_buffer[20], frame_buffer[21], frame_buffer[22],
                                  frame_buffer[23], frame_buffer[24], frame_buffer[25], frame_buffer[26],
                                  frame_buffer[27], frame_buffer[28], frame_buffer[29], frame_buffer[30]};
                    
                    // 提取接收到的CRC值(字节31)
                    crc_received <= frame_buffer[31];
                    
                    // 计算CRC值(基于包号+用户数据区，共28字节)
                    // 这里需要多个时钟周期来计算，简化处理
                    crc_calc <= calculate_crc();
                    
                    // 比较CRC值
                    crc_valid <= (crc_calc == crc_received);
                end
                
                DONE: begin
                    parse_done <= 1'b1;
                end
                
                ERROR: begin
                    parse_error <= 1'b1;
                end
            endcase
        end
    end

    // CRC计算函数(简化实现，实际应该用状态机处理)
    function [7:0] calculate_crc;
        integer i;
        reg [7:0] crc_temp;
        reg [7:0] data_temp;
        begin
            crc_temp = 8'd0;
            // 计算包号的CRC(字节3-6)
            for (i = 3; i <= 6; i = i + 1) begin
                crc_temp = crc8_step(frame_buffer[i], crc_temp);
            end
            // 计算用户数据的CRC(字节7-30)
            for (i = 7; i <= 30; i = i + 1) begin
                crc_temp = crc8_step(frame_buffer[i], crc_temp);
            end
            calculate_crc = crc_temp;
        end
    endfunction

    // CRC8单步计算函数
    function [7:0] crc8_step;
        input [7:0] data;
        input [7:0] crc;
        reg [7:0] data_xor_crc;
        begin
            data_xor_crc = data ^ crc;
            crc8_step[0] = data_xor_crc[0] ^ data_xor_crc[4] ^ data_xor_crc[5];
            crc8_step[1] = data_xor_crc[1] ^ data_xor_crc[5] ^ data_xor_crc[6];
            crc8_step[2] = data_xor_crc[2] ^ data_xor_crc[6] ^ data_xor_crc[7];
            crc8_step[3] = data_xor_crc[3] ^ data_xor_crc[7];
            crc8_step[4] = data_xor_crc[0] ^ data_xor_crc[4];
            crc8_step[5] = data_xor_crc[0] ^ data_xor_crc[1] ^ data_xor_crc[4] ^ data_xor_crc[5];
            crc8_step[6] = data_xor_crc[1] ^ data_xor_crc[2] ^ data_xor_crc[5] ^ data_xor_crc[6];
            crc8_step[7] = data_xor_crc[2] ^ data_xor_crc[3] ^ data_xor_crc[6] ^ data_xor_crc[7];
        end
    endfunction

endmodule
