//==============================================================================
// 数据解析模块
// 解析32字节数据帧，包含帧头、长度、包号、用户数据和CRC8校验
// 帧格式: 帧头(2B) + 长度(1B) + 包号(4B) + 用户数据(24B) + CRC8(1B)
// 基于1-Wire总线协议，包含实际的CRC计算状态机
//==============================================================================

module data_parser (
    input         clk,           // 时钟信号
    input         rst_n,         // 复位信号，低有效
    input  [7:0]  data_in,       // 输入数据，来自1-Wire接口
    input         data_valid,    // 数据有效信号

    output reg [31:0] packet_no,     // 解析出的包号
    output reg [191:0] user_data,    // 解析出的用户数据(24字节)
    output reg        crc_valid,     // CRC校验结果
    output reg        parse_done,    // 解析完成信号
    output reg        parse_error    // 解析错误信号
);

    // 主状态机状态定义
    localparam IDLE         = 4'b0000;
    localparam HEADER_CHECK = 4'b0001;
    localparam LENGTH_CHECK = 4'b0010;
    localparam DATA_RECEIVE = 4'b0011;
    localparam CRC_CALC     = 4'b0100;
    localparam CRC_CHECK    = 4'b0101;
    localparam DONE         = 4'b0110;
    localparam ERROR        = 4'b0111;

    // CRC计算状态机状态定义
    localparam CRC_IDLE     = 2'b00;
    localparam CRC_CALC_ST  = 2'b01;
    localparam CRC_DONE     = 2'b10;

    // 主状态机信号
    reg [3:0]   state, next_state;
    reg [4:0]   byte_cnt;           // 字节计数器(0-31)
    reg [7:0]   frame_buffer [0:31]; // 32字节帧缓存
    reg [7:0]   crc_received;       // 接收到的CRC值

    // CRC计算状态机信号
    reg [1:0]   crc_state, crc_next_state;
    reg [7:0]   crc_data_in;
    reg [7:0]   crc_current;
    wire [7:0]  crc_next;
    reg [4:0]   crc_byte_cnt;       // CRC计算字节计数器(3-30, 共28字节)
    reg [7:0]   crc_calc_result;    // CRC计算结果
    reg         crc_calc_start;     // CRC计算启动信号
    reg         crc_calc_done;      // CRC计算完成信号

    // 实例化CRC8计算模块
    crc8_calc u_crc8_calc (
        .data_in(crc_data_in),
        .crc_in(crc_current),
        .crc_out(crc_next)
    );

    // 主状态机时序逻辑
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            state <= IDLE;
        end else begin
            state <= next_state;
        end
    end

    // 主状态机组合逻辑
    always @(*) begin
        next_state = state;
        case (state)
            IDLE: begin
                if (data_valid && data_in == 8'hAA)
                    next_state = HEADER_CHECK;
            end

            HEADER_CHECK: begin
                if (data_valid) begin
                    if (data_in == 8'h55)
                        next_state = LENGTH_CHECK;
                    else
                        next_state = ERROR;
                end
            end

            LENGTH_CHECK: begin
                if (data_valid) begin
                    if (data_in == 8'h20)  // 长度为32字节
                        next_state = DATA_RECEIVE;
                    else
                        next_state = ERROR;
                end
            end

            DATA_RECEIVE: begin
                if (data_valid && byte_cnt == 5'd31)
                    next_state = CRC_CALC;
            end

            CRC_CALC: begin
                if (crc_calc_done)
                    next_state = CRC_CHECK;
            end

            CRC_CHECK: begin
                next_state = DONE;
            end

            DONE: begin
                next_state = IDLE;
            end

            ERROR: begin
                next_state = IDLE;
            end

            default: next_state = IDLE;
        endcase
    end

    // CRC计算状态机时序逻辑
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            crc_state <= CRC_IDLE;
        end else begin
            crc_state <= crc_next_state;
        end
    end

    // CRC计算状态机组合逻辑
    always @(*) begin
        crc_next_state = crc_state;
        case (crc_state)
            CRC_IDLE: begin
                if (crc_calc_start)
                    crc_next_state = CRC_CALC_ST;
            end

            CRC_CALC_ST: begin
                if (crc_byte_cnt == 5'd30)  // 计算完28字节(3-30)
                    crc_next_state = CRC_DONE;
            end

            CRC_DONE: begin
                crc_next_state = CRC_IDLE;
            end

            default: crc_next_state = CRC_IDLE;
        endcase
    end

    // 主状态机数据处理逻辑
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            byte_cnt <= 5'd0;
            packet_no <= 32'd0;
            user_data <= 192'd0;
            crc_valid <= 1'b0;
            parse_done <= 1'b0;
            parse_error <= 1'b0;
            crc_received <= 8'd0;
            crc_calc_start <= 1'b0;
        end else begin
            case (state)
                IDLE: begin
                    byte_cnt <= 5'd0;
                    parse_done <= 1'b0;
                    parse_error <= 1'b0;
                    crc_valid <= 1'b0;
                    crc_calc_start <= 1'b0;
                    if (data_valid && data_in == 8'hAA) begin
                        frame_buffer[0] <= data_in;
                        byte_cnt <= 5'd1;
                    end
                end

                HEADER_CHECK: begin
                    if (data_valid) begin
                        frame_buffer[byte_cnt] <= data_in;
                        byte_cnt <= byte_cnt + 1'b1;
                    end
                end

                LENGTH_CHECK: begin
                    if (data_valid) begin
                        frame_buffer[byte_cnt] <= data_in;
                        byte_cnt <= byte_cnt + 1'b1;
                    end
                end

                DATA_RECEIVE: begin
                    if (data_valid) begin
                        frame_buffer[byte_cnt] <= data_in;
                        byte_cnt <= byte_cnt + 1'b1;
                    end
                end

                CRC_CALC: begin
                    // 启动CRC计算
                    if (!crc_calc_start && !crc_calc_done) begin
                        crc_calc_start <= 1'b1;
                    end else begin
                        crc_calc_start <= 1'b0;
                    end
                end

                CRC_CHECK: begin
                    // 提取包号(字节3-6，大端序)
                    packet_no <= {frame_buffer[3], frame_buffer[4], frame_buffer[5], frame_buffer[6]};

                    // 提取用户数据(字节7-30)
                    user_data <= {frame_buffer[7],  frame_buffer[8],  frame_buffer[9],  frame_buffer[10],
                                  frame_buffer[11], frame_buffer[12], frame_buffer[13], frame_buffer[14],
                                  frame_buffer[15], frame_buffer[16], frame_buffer[17], frame_buffer[18],
                                  frame_buffer[19], frame_buffer[20], frame_buffer[21], frame_buffer[22],
                                  frame_buffer[23], frame_buffer[24], frame_buffer[25], frame_buffer[26],
                                  frame_buffer[27], frame_buffer[28], frame_buffer[29], frame_buffer[30]};

                    // 提取接收到的CRC值(字节31)
                    crc_received <= frame_buffer[31];

                    // 比较CRC值
                    crc_valid <= (crc_calc_result == crc_received);
                end

                DONE: begin
                    parse_done <= 1'b1;
                end

                ERROR: begin
                    parse_error <= 1'b1;
                end
            endcase
        end
    end

    // CRC计算状态机逻辑
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            crc_current <= 8'd0;
            crc_data_in <= 8'd0;
            crc_byte_cnt <= 5'd0;
            crc_calc_result <= 8'd0;
            crc_calc_done <= 1'b0;
        end else begin
            case (crc_state)
                CRC_IDLE: begin
                    crc_current <= 8'd0;
                    crc_byte_cnt <= 5'd3;  // 从字节3开始(包号第一字节)
                    crc_calc_done <= 1'b0;
                    if (crc_calc_start) begin
                        crc_data_in <= frame_buffer[3];  // 第一个字节
                    end
                end

                CRC_CALC_ST: begin
                    // 更新CRC值
                    crc_current <= crc_next;

                    // 准备下一个字节
                    if (crc_byte_cnt < 5'd30) begin
                        crc_byte_cnt <= crc_byte_cnt + 1'b1;
                        crc_data_in <= frame_buffer[crc_byte_cnt + 1'b1];
                    end else begin
                        // 计算完成，保存结果
                        crc_calc_result <= crc_next;
                    end
                end

                CRC_DONE: begin
                    crc_calc_done <= 1'b1;
                end
            endcase
        end
    end

endmodule
