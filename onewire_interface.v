//==============================================================================
// 1-Wire总线接口模块
// 实现1-Wire总线物理层协议，将位流转换为字节流
// 支持复位检测、存在脉冲检测、数据位读取
// 假设系统时钟为100MHz (10ns周期)
//==============================================================================

module onewire_interface (
    input         clk,           // 系统时钟 100MHz
    input         rst_n,         // 复位信号，低有效
    inout         onewire_io,    // 1-Wire总线信号(双向)
    
    output reg [7:0] byte_data,  // 输出字节数据
    output reg       byte_valid, // 字节数据有效信号
    output reg       reset_detected, // 复位脉冲检测
    output reg       presence_detected, // 存在脉冲检测
    output reg       error_flag  // 错误标志
);

    // 时序参数定义 (基于100MHz时钟)
    localparam RESET_MIN_TIME    = 16'd48000;  // 复位脉冲最小时间 480us
    localparam RESET_MAX_TIME    = 16'd96000;  // 复位脉冲最大时间 960us
    localparam PRESENCE_MIN_TIME = 16'd6000;   // 存在脉冲最小时间 60us
    localparam PRESENCE_MAX_TIME = 16'd24000;  // 存在脉冲最大时间 240us
    localparam SLOT_TIME         = 16'd6000;   // 时隙时间 60us
    localparam SAMPLE_TIME       = 16'd1500;   // 采样时间 15us
    localparam RECOVERY_TIME     = 16'd100;    // 恢复时间 1us

    // 状态定义
    localparam IDLE              = 4'b0000;
    localparam RESET_DETECT      = 4'b0001;
    localparam PRESENCE_WAIT     = 4'b0010;
    localparam PRESENCE_DETECT   = 4'b0011;
    localparam BIT_START         = 4'b0100;
    localparam BIT_SAMPLE        = 4'b0101;
    localparam BIT_RECOVERY      = 4'b0110;
    localparam BYTE_COMPLETE     = 4'b0111;
    localparam ERROR             = 4'b1000;

    // 内部信号
    reg [3:0]   state, next_state;
    reg [15:0]  timer;              // 时序计数器
    reg [2:0]   bit_cnt;            // 位计数器 (0-7)
    reg [7:0]   shift_reg;          // 移位寄存器
    reg         onewire_reg;        // 总线电平寄存器
    reg         onewire_prev;       // 前一时钟的总线电平
    reg         falling_edge;       // 下降沿检测
    reg         rising_edge;        // 上升沿检测
    reg         low_pulse_start;    // 低脉冲开始标志
    reg [15:0]  low_pulse_time;     // 低脉冲持续时间

    // 总线电平检测和边沿检测
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            onewire_reg <= 1'b1;
            onewire_prev <= 1'b1;
            falling_edge <= 1'b0;
            rising_edge <= 1'b0;
        end else begin
            onewire_reg <= onewire_io;
            onewire_prev <= onewire_reg;
            falling_edge <= onewire_prev & ~onewire_reg;
            rising_edge <= ~onewire_prev & onewire_reg;
        end
    end

    // 低脉冲时间测量
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            low_pulse_start <= 1'b0;
            low_pulse_time <= 16'd0;
        end else begin
            if (falling_edge) begin
                low_pulse_start <= 1'b1;
                low_pulse_time <= 16'd0;
            end else if (rising_edge) begin
                low_pulse_start <= 1'b0;
            end else if (low_pulse_start) begin
                low_pulse_time <= low_pulse_time + 1'b1;
            end
        end
    end

    // 状态机时序逻辑
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            state <= IDLE;
        end else begin
            state <= next_state;
        end
    end

    // 状态机组合逻辑
    always @(*) begin
        next_state = state;
        case (state)
            IDLE: begin
                if (falling_edge)
                    next_state = RESET_DETECT;
            end
            
            RESET_DETECT: begin
                if (rising_edge) begin
                    if (low_pulse_time >= RESET_MIN_TIME && low_pulse_time <= RESET_MAX_TIME)
                        next_state = PRESENCE_WAIT;
                    else if (low_pulse_time < SLOT_TIME)
                        next_state = BIT_START;
                    else
                        next_state = ERROR;
                end
            end
            
            PRESENCE_WAIT: begin
                if (timer >= PRESENCE_MAX_TIME)
                    next_state = BIT_START;
                else if (falling_edge)
                    next_state = PRESENCE_DETECT;
            end
            
            PRESENCE_DETECT: begin
                if (rising_edge) begin
                    if (low_pulse_time >= PRESENCE_MIN_TIME && low_pulse_time <= PRESENCE_MAX_TIME)
                        next_state = BIT_START;
                    else
                        next_state = ERROR;
                end
            end
            
            BIT_START: begin
                if (falling_edge)
                    next_state = BIT_SAMPLE;
            end
            
            BIT_SAMPLE: begin
                if (timer >= SAMPLE_TIME)
                    next_state = BIT_RECOVERY;
            end
            
            BIT_RECOVERY: begin
                if (timer >= SLOT_TIME) begin
                    if (bit_cnt == 3'd7)
                        next_state = BYTE_COMPLETE;
                    else
                        next_state = BIT_START;
                end
            end
            
            BYTE_COMPLETE: begin
                next_state = BIT_START;
            end
            
            ERROR: begin
                next_state = IDLE;
            end
            
            default: next_state = IDLE;
        endcase
    end

    // 状态机数据处理逻辑
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            timer <= 16'd0;
            bit_cnt <= 3'd0;
            shift_reg <= 8'd0;
            byte_data <= 8'd0;
            byte_valid <= 1'b0;
            reset_detected <= 1'b0;
            presence_detected <= 1'b0;
            error_flag <= 1'b0;
        end else begin
            case (state)
                IDLE: begin
                    timer <= 16'd0;
                    bit_cnt <= 3'd0;
                    byte_valid <= 1'b0;
                    reset_detected <= 1'b0;
                    presence_detected <= 1'b0;
                    error_flag <= 1'b0;
                end
                
                RESET_DETECT: begin
                    if (rising_edge && low_pulse_time >= RESET_MIN_TIME && low_pulse_time <= RESET_MAX_TIME) begin
                        reset_detected <= 1'b1;
                        timer <= 16'd0;
                    end
                end
                
                PRESENCE_WAIT: begin
                    timer <= timer + 1'b1;
                end
                
                PRESENCE_DETECT: begin
                    if (rising_edge && low_pulse_time >= PRESENCE_MIN_TIME && low_pulse_time <= PRESENCE_MAX_TIME) begin
                        presence_detected <= 1'b1;
                    end
                end
                
                BIT_START: begin
                    timer <= 16'd0;
                end
                
                BIT_SAMPLE: begin
                    timer <= timer + 1'b1;
                    if (timer == SAMPLE_TIME) begin
                        // 采样数据位 (LSB first)
                        shift_reg <= {onewire_reg, shift_reg[7:1]};
                    end
                end
                
                BIT_RECOVERY: begin
                    timer <= timer + 1'b1;
                    if (timer == SLOT_TIME) begin
                        bit_cnt <= bit_cnt + 1'b1;
                        timer <= 16'd0;
                    end
                end
                
                BYTE_COMPLETE: begin
                    byte_data <= shift_reg;
                    byte_valid <= 1'b1;
                    bit_cnt <= 3'd0;
                    shift_reg <= 8'd0;
                end
                
                ERROR: begin
                    error_flag <= 1'b1;
                end
            endcase
        end
    end

    // 1-Wire总线为开漏输出，只能拉低，不能驱动高电平
    // 这里假设为只读模式，不实现写功能
    assign onewire_io = 1'bz;  // 高阻态，只读

endmodule
