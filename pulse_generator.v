module pulse_generator #(
    parameter CLK_FREQ = 125_000_000,    // 时钟频率125MHz
    parameter MIN_PULSE_WIDTH_US = 40    // 最小脉冲宽度40us
)(
    input               clk,                    // 系统时钟
    input               rst_n,                  // 复位信号，低电平有效
    input  [31:0]       fpga_trigger_time,     // 脉冲周期时间(us)，最小1ms
    input               fpga_trigger_method,   // 触发方法选择(1=启用)
    input               device_start_stop,     // 设备启停信号
    input  [31:0]       pulse_width_us,        // 脉冲宽度配置(us)，最小40us
    output reg          pulse_out              // 脉冲输出信号
);

// 参数计算
localparam US_CYCLES = CLK_FREQ / 1_000_000;           // 1us对应的时钟周期数
localparam MIN_WIDTH_CYCLES = MIN_PULSE_WIDTH_US * US_CYCLES; // 最小脉冲宽度对应的时钟周期数

// 状态定义
localparam IDLE    = 2'b00;
localparam RUNNING = 2'b01;

// 信号定义
reg [1:0]  state, next_state;
reg [1:0]  device_start_stop_sync;     // 同步寄存器
reg        device_start_stop_pos_edge; // 上升沿检测
reg        device_start_stop_neg_edge; // 下降沿检测

reg [31:0] period_counter;             // 周期计数器
reg [31:0] width_counter;              // 脉冲宽度计数器
reg [31:0] period_cycles;              // 周期对应的时钟周期数
reg [31:0] width_cycles;               // 脉冲宽度对应的时钟周期数

reg        pulse_active;               // 脉冲激活标志
reg        period_done;                // 周期完成标志
reg        width_done;                 // 宽度完成标志

// 边沿检测
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        device_start_stop_sync <= 2'b00;
    end else begin
        device_start_stop_sync <= {device_start_stop_sync[0], device_start_stop};
    end
end

always @(*) begin
    device_start_stop_pos_edge = ~device_start_stop_sync[1] & device_start_stop_sync[0];
    device_start_stop_neg_edge = device_start_stop_sync[1] & ~device_start_stop_sync[0];
end

// 计算周期和宽度对应的时钟周期数
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        period_cycles <= 1_000_000; // 默认1ms
        width_cycles <= MIN_WIDTH_CYCLES;
    end else begin
        // fpga_trigger_time单位为us，转换为时钟周期数
        period_cycles <= fpga_trigger_time * US_CYCLES;
        
        // pulse_width_us单位为us，转换为时钟周期数，确保不小于最小值
        if (pulse_width_us < MIN_PULSE_WIDTH_US) begin
            width_cycles <= MIN_WIDTH_CYCLES;
        end else begin
            width_cycles <= pulse_width_us * US_CYCLES;
        end
    end
end

// 状态机
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        state <= IDLE;
    end else begin
        state <= next_state;
    end
end

always @(*) begin
    next_state = state;
    case (state)
        IDLE: begin
            if (device_start_stop_pos_edge && fpga_trigger_method) begin
                next_state = RUNNING;
            end
        end
        RUNNING: begin
            if (device_start_stop_neg_edge) begin
                next_state = IDLE;
            end
        end
        default: next_state = IDLE;
    endcase
end

// 周期计数器
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        period_counter <= 32'd0;
        period_done <= 1'b0;
    end else if (state == IDLE) begin
        period_counter <= 32'd0;
        period_done <= 1'b0;
    end else if (state == RUNNING) begin
        if (period_counter >= period_cycles - 1) begin
            period_counter <= 32'd0;
            period_done <= 1'b1;
        end else begin
            period_counter <= period_counter + 1;
            period_done <= 1'b0;
        end
    end else begin
        period_counter <= 32'd0;
        period_done <= 1'b0;
    end
end

// 脉冲宽度计数器
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        width_counter <= 32'd0;
        width_done <= 1'b0;
        pulse_active <= 1'b0;
    end else if (state == IDLE) begin
        width_counter <= 32'd0;
        width_done <= 1'b0;
        pulse_active <= 1'b0;
    end else if (state == RUNNING) begin
        if (period_done || (period_counter == 32'd0 && !pulse_active)) begin
            // 开始新的脉冲
            pulse_active <= 1'b1;
            width_counter <= 32'd0;
            width_done <= 1'b0;
        end else if (pulse_active) begin
            if (width_counter >= width_cycles - 1) begin
                width_counter <= 32'd0;
                width_done <= 1'b1;
                pulse_active <= 1'b0;
            end else begin
                width_counter <= width_counter + 1;
                width_done <= 1'b0;
            end
        end
    end
end

// 脉冲输出
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        pulse_out <= 1'b0;
    end else if (state == RUNNING && pulse_active && !width_done) begin
        pulse_out <= 1'b1;
    end else begin
        pulse_out <= 1'b0;
    end
end

endmodule
